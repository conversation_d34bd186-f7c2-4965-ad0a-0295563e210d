/**
 * Represents a trading signal.
 * 'BUY' indicates a long position should be opened.
 * 'SELL' indicates a short position should be opened.
 * 'HOLD' indicates no action should be taken.
 */
type Signal = 'BUY' | 'SELL' | 'HOLD'

/**
 * Represents a single data point for a financial instrument.
 * Requires high, low, and close prices for SuperTrend calculation.
 */
interface DataPoint {
  high: number
  low: number
  close: number
}

/**
 * A helper class to calculate a single SuperTrend indicator value and trend.
 * This class maintains its own state for ATR and trend calculation.
 */
class SuperTrendIndicator {
  private atrPeriod: number
  private multiplier: number
  private atr: number | null = null
  private trueRangeHistory: number[] = []
  public trend: 'UP' | 'DOWN' | 'NONE' = 'NONE'
  public value: number | null = null
  private upperBand: number = 0
  private lowerBand: number = 0

  constructor(atrPeriod: number, multiplier: number) {
    this.atrPeriod = atrPeriod
    this.multiplier = multiplier
  }

  /**
   * Updates the indicator with a new data point.
   * @param dataPoint The current data point (candle).
   * @param prevDataPoint The previous data point (candle).
   */
  public update(dataPoint: DataPoint, prevDataPoint: DataPoint | null): void {
    if (!prevDataPoint) {
      this.trueRangeHistory.push(dataPoint.high - dataPoint.low)
      return
    }

    // Calculate True Range
    const trueRange = Math.max(
      dataPoint.high - dataPoint.low,
      Math.abs(dataPoint.high - prevDataPoint.close),
      Math.abs(dataPoint.low - prevDataPoint.close)
    )

    // Calculate ATR
    if (this.atr === null) {
      this.trueRangeHistory.push(trueRange)
      if (this.trueRangeHistory.length >= this.atrPeriod) {
        const sum = this.trueRangeHistory.reduce((a, b) => a + b, 0)
        this.atr = sum / this.atrPeriod
      }
    } else {
      this.atr = (this.atr * (this.atrPeriod - 1) + trueRange) / this.atrPeriod
    }

    if (this.atr === null) return

    // Calculate and update SuperTrend value
    const basicUpperBand = (dataPoint.high + dataPoint.low) / 2 + this.multiplier * this.atr
    const basicLowerBand = (dataPoint.high + dataPoint.low) / 2 - this.multiplier * this.atr

    this.upperBand =
      basicUpperBand < this.upperBand || prevDataPoint.close > this.upperBand
        ? basicUpperBand
        : this.upperBand
    this.lowerBand =
      basicLowerBand > this.lowerBand || prevDataPoint.close < this.lowerBand
        ? basicLowerBand
        : this.lowerBand

    if (this.value === null) {
      // Initial state
      this.trend = 'UP'
      this.value = this.lowerBand
      return
    }

    if (this.value === this.upperBand && dataPoint.close > this.upperBand) {
      this.trend = 'UP'
      this.value = this.lowerBand
    } else if (this.value === this.lowerBand && dataPoint.close < this.lowerBand) {
      this.trend = 'DOWN'
      this.value = this.upperBand
    } else {
      this.value = this.trend === 'UP' ? this.lowerBand : this.upperBand
    }
  }
}

/**
 * Defines the parameters for the SuperTrend trading strategy.
 */
interface SuperTrendStrategyOptions {
  superTrends: { atrPeriod: number; multiplier: number }[]
  takeProfitPercentage?: number
  stopLossPercentage?: number
}

/**
 * Implements a trading strategy based on multiple SuperTrend indicators.
 * A BUY signal is generated when the price is above all SuperTrend lines.
 * A SELL signal is generated when the price is below all SuperTrend lines.
 */
class SuperTrendStrategy {
  private indicators: SuperTrendIndicator[]
  private takeProfitPercentage?: number
  private stopLossPercentage?: number
  private prevDataPoint: DataPoint | null = null
  private currentSignal: Signal = 'HOLD'

  constructor(options: SuperTrendStrategyOptions) {
    this.indicators = options.superTrends.map(
      (p) => new SuperTrendIndicator(p.atrPeriod, p.multiplier)
    )
    this.takeProfitPercentage = options.takeProfitPercentage
    this.stopLossPercentage = options.stopLossPercentage
  }

  /**
   * Updates the strategy with a new data point and returns a trading signal.
   * @param dataPoint The latest data point (e.g., a new candle).
   * @returns A trading signal ('BUY', 'SELL', or 'HOLD').
   */
  public update(dataPoint: DataPoint): Signal {
    this.indicators.forEach((indicator) => indicator.update(dataPoint, this.prevDataPoint))
    this.prevDataPoint = dataPoint

    const allTrends = this.indicators.map((i) => i.trend)

    if (allTrends.includes('NONE')) {
      return 'HOLD' // Not enough data yet
    }

    const isBuySignal = allTrends.every((t) => t === 'UP')
    const isSellSignal = allTrends.every((t) => t === 'DOWN')

    let newSignal: Signal = 'HOLD'
    if (isBuySignal) {
      newSignal = 'BUY'
    } else if (isSellSignal) {
      newSignal = 'SELL'
    }

    // Only return a new signal if the state has changed to avoid repeated signals
    if (newSignal !== 'HOLD' && newSignal !== this.currentSignal) {
      this.currentSignal = newSignal
      return newSignal
    }

    // If the trend has ended (neither buy nor sell), reset the signal
    if (newSignal === 'HOLD') {
      this.currentSignal = 'HOLD'
    }

    return 'HOLD'
  }

  /**
   * Calculates the take profit price for a given entry price.
   * @param entryPrice The price at which the trade was entered.
   * @returns The take profit price, or null if not configured.
   */
  public getTakeProfitPrice(entryPrice: number): number | null {
    if (!this.takeProfitPercentage) return null
    return entryPrice * (1 + this.takeProfitPercentage / 100)
  }

  /**
   * Calculates the stop loss price for a given entry price.
   * @param entryPrice The price at which the trade was entered.
   * @returns The stop loss price, or null if not configured.
   */
  public getStopLossPrice(entryPrice: number): number | null {
    if (!this.stopLossPercentage) return null
    return entryPrice * (1 - this.stopLossPercentage / 100)
  }
}
